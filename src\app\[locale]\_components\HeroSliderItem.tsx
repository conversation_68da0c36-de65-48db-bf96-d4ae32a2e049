import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { SlideAnim, ScaleAnim } from "../../animations/Animations";
import { SliderItemType } from "../slides-data";
import Text from "@/components/Text";
import QuotesIcon from "@/assets/imgs/quotes.svg";
import Image from "next/image";
import { Link } from "@/i18n/navigation";

type Props = { className?: string };

const HeroSliderItem = ({
  className,
  item,
}: Props & {
  item: SliderItemType;
}) => {
  return (
    <article className={cn("flex items-center max-sm:flex-col", className)}>
      <SlideAnim direction="left">
        <div
          className={cn(
            "z-10 flex w-full flex-col items-center lowercase sm:w-[60%]",
          )}
        >
          <Text
            as="h1"
            size="6xl"
            className={cn(
              "inline text-center leading-none font-bold",
              !item.subtitle && "sm:text-balance",
              item.withLineLarge && "leading-tight",
            )}
          >
            <QuotesIcon
              className={cn(
                "mdx:w-18 mr-2 inline w-10 shrink-0 sm:mr-4 sm:ml-2 sm:w-12",
              )}
            />
            <p className={cn("inline")}>
              {item.title}
              {!item.subtitle && (
                <QuotesIcon className="ml-2 inline w-6 shrink-0 rotate-180 sm:mt-4 sm:w-10" />
              )}
            </p>
          </Text>
          {item.featureText && (
            <Text size="lg" className="mt-8 text-center font-light">
              {item.featureText}
            </Text>
          )}
          {item.featureImg && (
            <item.featureImg className="mt-2 h-auto w-32 object-contain sm:w-48" />
          )}
          {item.subtitle && (
            <Text
              as="h4"
              size="xl"
              className="inline text-center sm:text-balance"
            >
              {item.subtitle}
              <QuotesIcon className="ml-2 inline w-6 rotate-180 sm:w-8" />
            </Text>
          )}
          <Text as="p" size="lg" className="mt-8 mb-2 font-extrabold">
            {item.date}
          </Text>
          <Link
            href="#product"
            className={cn(
              buttonVariants({ variant: "secondary" }),
              "font-sans-2 bg-secondary-foreground/90 w-fit tracking-wider",
            )}
          >
            {item.buttonText}
          </Link>
        </div>
      </SlideAnim>
      <ScaleAnim>
        <Image
          src={item.src}
          alt={item.alt}
          width={item.src.width}
          height={item.src.height}
          placeholder="blur"
          className="mdx:-mt-20 h-auto max-h-[40vh] w-full object-contain sm:max-h-[80vh] sm:w-[40%]"
          quality={90}
        />
      </ScaleAnim>
    </article>
  );
};

export default HeroSliderItem;
