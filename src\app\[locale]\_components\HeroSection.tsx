import SignatureImg from "@/assets/imgs/signature.svg";
import LatteLogoWhite from "@/assets/imgs/latte-logo-white.svg";
import RosePalharesLogoWhite from "@/assets/imgs/rose-palhares-logo-white.png";
import VisarWhite from "@/assets/imgs/visar-white.svg";
import Text from "@/components/Text";
import { cn } from "@/lib/utils";
import Image from "next/image";

import { getTranslations } from "next-intl/server";
import { ScaleAnim } from "../../animations/Animations";

type Props = { className?: string };

const HeroSection = async ({ className }: Props) => {
  const t = await getTranslations("HeroSection");
  return (
    <ScaleAnim selector="svg,span,img" stagger={true} options={{ delay: 0.3 }}>
      <article
        className={cn(
          "flex items-center justify-between gap-8 text-white max-sm:flex-col",
          className,
        )}
      >
        <div className="flex items-center gap-6 sm:w-[35%] sm:gap-8">
          <LatteLogoWhite className="h-auto w-20 object-contain sm:w-[20%]" />
          <Image
            src={RosePalharesLogoWhite}
            alt="Rose Palhares"
            className="h-auto w-36 object-contain sm:w-[40%]"
          />
          <VisarWhite className="h-auto w-28 object-contain sm:w-[30%]" />
        </div>
        <span className="flex items-start gap-2">
          <SignatureImg className="h-auto w sm:w-32 object-contain" />
          <Text as="p" size="lg" className="mt-2 sm:mt-1">
            {t("signatureText")}
          </Text>
        </span>
      </article>
    </ScaleAnim>
  );
};

export default HeroSection;
