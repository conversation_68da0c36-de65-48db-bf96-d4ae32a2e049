import SignatureImg from "@/assets/imgs/signature.svg";
import StoresLogosWhite from "@/assets/imgs/stores-logos-white.svg";
import LatteLogoWhite from "@/assets/imgs/latte-logo-white.svg";
import Text from "@/components/Text";
import { cn } from "@/lib/utils";

import { getTranslations } from "next-intl/server";
import { ScaleAnim } from "../../animations/Animations";

type Props = { className?: string };

const HeroSection = async ({ className }: Props) => {
  const t = await getTranslations("HeroSection");
  return (
    <ScaleAnim selector="svg,span" stagger={true} options={{ delay: 0.3 }}>
      <article
        className={cn(
          "flex items-start justify-between gap-8 text-white max-sm:flex-col",
          className,
        )}
      >
        <div className="flex items-center gap-6 sm:w-[30%] sm:gap-8">
          <LatteLogoWhite className="h-auto w-20 object-contain sm:w-24" />
          <StoresLogosWhite className="w-36 object-contain" />
        </div>
        <span className="flex flex-wrap items-start gap-2">
          <SignatureImg className="h-auto w-32 object-contain" />
          <Text as="p" size="lg" className="mt-2 sm:mt-1">
            {t("signatureText")}
          </Text>
        </span>
      </article>
    </ScaleAnim>
  );
};

export default HeroSection;
