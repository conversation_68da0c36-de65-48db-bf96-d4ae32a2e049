export const formatKwanza = (amount: number): string => {
  const formattedAmount = new Intl.NumberFormat("de", {
    style: "decimal",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    useGrouping: true,
  }).format(amount);

  // Add Euro equivalent for the main product price (295000 Kz = 220€)
  if (amount === 295000) {
    return `${formattedAmount} Kz (220€)`;
  }

  return `${formattedAmount} Kz`;
};
