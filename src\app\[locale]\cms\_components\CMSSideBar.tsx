"use client";

import SidebarLink from "./SidebarLink";
import { linksObj } from "@/app/links";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { AlignRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useRef, useEffect } from "react";

export default function CMSSideBar() {
  const t = useTranslations("CMS.sidebar");
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const firstLinkRef = useRef<HTMLAnchorElement>(null);

  useEffect(() => {
    if (isSidebarOpen && firstLinkRef.current) {
      firstLinkRef.current.focus();
    }
  }, [isSidebarOpen]);

  useEffect(() => {
    if (!isSidebarOpen) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setIsSidebarOpen(false);
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isSidebarOpen]);

  return (
    <>
      {isSidebarOpen && (
        <div
          onClick={() => setIsSidebarOpen(false)}
          aria-hidden="true"
          tabIndex={-1}
          className="bg-foreground/40 animate-in fade-in fixed inset-0 z-30 backdrop-blur-sm transition-opacity duration-300"
        />
      )}

      <aside className="sticky top-30 z-40 w-[min(100%,20rem)] flex-col">
        <Card>
          <CardHeader className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">{t("title")}</h1>
            <Button
              size="icon"
              className="p-1.5"
              aria-expanded={isSidebarOpen}
              aria-controls="cms-sidebar-nav"
              aria-label={isSidebarOpen ? t("closeSidebar") : t("openSidebar")}
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <AlignRight className="size-4" />
            </Button>
          </CardHeader>
          {isSidebarOpen && (
            <>
              <Separator />
              <CardContent
                ref={sidebarRef}
                className="animate-in slide-in-from-top-2 fade-in transition-all duration-300"
              >
                <nav
                  id="cms-sidebar-nav"
                  className="flex flex-col gap-2.5"
                  role="navigation"
                  aria-label={t("sidebarNavigation")}
                >
                  <SidebarLink
                    href={linksObj.cms.dashboard.href}
                    onClick={() => setIsSidebarOpen(false)}
                    ref={firstLinkRef}
                  >
                    {t("dashboard")}
                  </SidebarLink>
                  <SidebarLink
                    href={linksObj.cms.users.href}
                    onClick={() => setIsSidebarOpen(false)}
                  >
                    {t("users")}
                  </SidebarLink>
                </nav>
              </CardContent>
            </>
          )}
        </Card>
      </aside>
    </>
  );
}
