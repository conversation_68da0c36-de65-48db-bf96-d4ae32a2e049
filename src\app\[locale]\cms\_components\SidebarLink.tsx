"use client";

import { Link, usePathname } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { ReactNode, forwardRef } from "react";

interface SidebarLinkProps {
  href: string;
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  ref?: React.Ref<HTMLAnchorElement>;
}

function SidebarLink({
  href,
  children,
  onClick,
  className,
  ref,
}: SidebarLinkProps) {
  const pathname = usePathname();
  const isActive = pathname === href || pathname.startsWith(`${href}/`);
  return (
    <Link
      onClick={onClick}
      href={href}
      ref={ref}
      className={cn(
        "hocus:bg-primary-foreground/20 hocus:text-primary hocus:px-4 w-full rounded-md px-0 py-2 font-medium text-current duration-300 ease-out",
        className,
        {
          "bg-primary hocus:bg-primary hocus:text-accent text-accent px-4":
            isActive,
        },
      )}
    >
      {children}
    </Link>
  );
}

export default SidebarLink;
