import { getTranslations } from "next-intl/server";

import sneakersTopBottom from "@/assets/imgs/sneakers-top-bottom.png";
import sneakerSideLeftRotate from "@/assets/imgs/sneaker-side-left-rotate.png";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import sneakerSideRightRotate from "@/assets/imgs/sneaker-side-right-rotate.png";
import latteLogo from "@/assets/imgs/latte-logo.svg";
import { z } from "zod";

const slideSchema = z.object({
  title: z.string(),
  subtitle: z.string().optional(),
  featureText: z.string().optional(),
  featureImg: z.any().optional(),
  date: z.string(),
  buttonText: z.string(),
  src: z.any(),
  alt: z.string(),
  withLineLarge: z.boolean().optional(),
});

export type SliderItemType = z.infer<typeof slideSchema>;
export const getSliders = async () => {
  const t = await getTranslations("HeroSection");
  const slides = t.raw("slides") as any[];

  const images = [
    sneakerSideRightRotate,
    sneakerSideRightRotate,
    sneakerSideLeft,
    sneakerSideLeftRotate,
    sneakersTopBottom,
  ];

  const alts = [
    "Sneaker hero view for Latte event",
    "Sneaker side right rotated view",
    "Sneaker side left view",
    "Sneaker side left rotated view",
    "Sneaker top and bottom view",
  ];

  return slideSchema.array().parse(
    slides.map((slide, index) => ({
      ...slide,
      src: images[index],
      alt: alts[index],
      ...(index === 0 && { featureImg: latteLogo }),
      ...(index === 3 && { withLineLarge: true }),
    })),
  );
};
