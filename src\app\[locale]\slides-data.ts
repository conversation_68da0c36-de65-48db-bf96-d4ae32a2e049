import { getTranslations } from "next-intl/server";

import sneakersTopBottom from "@/assets/imgs/sneakers-top-bottom.png";
import sneakerSideLeftRotate from "@/assets/imgs/sneaker-side-left-rotate.png";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import sneakerSideRightRotate from "@/assets/imgs/sneaker-side-right-rotate.png";
import sneakerHero from "@/assets/imgs/sneaker-hero.png";

export const getSliders = async () => {
  const t = await getTranslations("HeroSection");
  return [
    {
      title: t("slide1Title"),
      subtitle: t("slide1Subtitle"),
      date: t("slide1Date"),
      src: sneakerHero,
      alt: "Sneaker hero view for Latte event",
    },
    {
      title: t("slide2Title"),
      subtitle: t("slide2Subtitle"),
      src: sneakerSideRightRotate,
      alt: "Sneaker side right rotated view",
    },
    {
      title: t("slide3Title"),
      src: sneakerSideLeft,
      alt: "Sneaker side left view",
    },
    {
      title: t("slide4Title"),
      subtitle: t("slide4Subtitle"),
      src: sneakerSideLeftRotate,
      alt: "Sneaker side left rotated view",
      withLineLarge: true,
    },
    {
      title: t("slide5Title"),
      subtitle: t("slide5Subtitle"),
      src: sneakersTopBottom,
      alt: "Sneaker top and bottom view",
    },
  ];
};

export type SliderItemType = Awaited<ReturnType<typeof getSliders>>[number];
