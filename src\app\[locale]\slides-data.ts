import { getTranslations } from "next-intl/server";

import sneakersTopBottom from "@/assets/imgs/sneakers-top-bottom.png";
import sneakerSideLeftRotate from "@/assets/imgs/sneaker-side-left-rotate.png";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import sneakerSideRightRotate from "@/assets/imgs/sneaker-side-right-rotate.png";
import sneakerHero from "@/assets/imgs/sneaker-hero.png";
import latteLogo from "@/assets/imgs/latte-logo.svg";

export const getSliders = async () => {
  const t = await getTranslations("HeroSection");
  const slides = t("slides") as any[];

  const images = [
    sneakerHero,
    sneakerSideRightRotate,
    sneakerSideLeft,
    sneakerSideLeftRotate,
    sneakersTopBottom,
  ];

  const alts = [
    "Sneaker hero view for Latte event",
    "Sneaker side right rotated view",
    "Sneaker side left view",
    "Sneaker side left rotated view",
    "Sneaker top and bottom view",
  ];

  return slides.map((slide, index) => ({
    ...slide,
    src: images[index],
    alt: alts[index],
    ...(index === 0 && { featureImg: latteLogo }), // Add Latte logo to first slide
    ...(index === 3 && { withLineLarge: true }), // Keep the withLineLarge for the 4th slide
  }));
};

export type SliderItemType = Awaited<ReturnType<typeof getSliders>>[number];
