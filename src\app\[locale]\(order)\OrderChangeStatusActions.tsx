"use client";

import { updateOrderStatus } from "@/app/[locale]/cms/actions";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useRouter } from "@/i18n/navigation";
import { OrderStatus } from "@/generated/prisma";
import { useTranslations } from "next-intl";
import { useTransition } from "react";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

type OrderCardAdminActionsProps = {
  isOpen: boolean;
  newStatus: OrderStatus | null;
  title: string;
  description: string;
  orderId: string;
  onClose: () => void;
};

export default function OrderChangeStatusActions({
  description,
  isOpen,
  newStatus,
  onClose,
  title,
  orderId,
}: OrderCardAdminActionsProps) {
  const tCMS = useTranslations("CMS");
  const [isPending, startTransition] = useTransition();
  const { refresh } = useRouter();

  const confirmStatusChange = () => {
    if (!newStatus) return;

    startTransition(async () => {
      try {
        const result = await updateOrderStatus({
          orderId,
          newStatus,
        });

        if (result.success) {
          toast.success(tCMS("actions.updateSuccess"));
          refresh();
          onClose();
        } else {
          toast.error(result.data);
        }
      } catch (_) {
        toast.error(tCMS("actions.updateError"));
      }
    });
  };

  return (
    <>
      <AlertDialog open={isOpen} onOpenChange={isPending ? undefined : onClose}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>{description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>
              {tCMS("actions.cancel")}
            </AlertDialogCancel>
            <Button
              onClick={confirmStatusChange}
              disabled={isPending}
              size="sm"
            >
              {isPending ? tCMS("actions.updating") : tCMS("actions.confirm")}
              {isPending && <Loader2 className="size-4 animate-spin" />}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
